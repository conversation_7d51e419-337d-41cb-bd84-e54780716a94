{"name": "connectrx", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@splidejs/splide": "^4.1.4", "@tabler/icons-vue": "^3.34.1", "@tailwindcss/vite": "^4.1.11", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.4"}}