<script setup>
import Header from '@/components/Header.vue';
import HeroSection from '@/components/HeroSection.vue';
import UnleashRomanceSection from '@/components/UnleashRomanceSection.vue';
import FeaturesSection from '@/components/FeaturesSection.vue';
import ProductHighlights from '@/components/ProductHighlights.vue';
import TestimonialsSection from '@/components/TestimonialsSection.vue';
import FixItTogetherSection from '@/components/FixItTogetherSection.vue';
import FAQSection from '@/components/FAQSection.vue';
import CTASection from '@/components/CTASection.vue';
import Footer from '@/components/Footer.vue';
</script>

<template>
  <div>
    <Header />
    <HeroSection />
    <UnleashRomanceSection />
    <FeaturesSection />
    <ProductHighlights />
    <TestimonialsSection />
    <FixItTogetherSection />
    <FAQSection />
    <CTASection />
    <Footer />
  </div>
</template>
