<script setup>
import Header from "@/components/Header.vue";
import Footer from "@/components/Footer.vue";
import products from "@/data/products.json";
import { computed, ref } from 'vue';
import ProductItem from "@/components/ProductItem.vue";
import { useRoute } from "vue-router";
import { IconChevronRight, IconMoodSad, IconFilter, IconX } from '@tabler/icons-vue'

const route = useRoute();

const activeCategorySlug = computed(() => route.params.category_slug || null);

const categories = computed(() => {
  const map = new Map();
  products.forEach(p => {
    if (!map.has(p.category_slug)) map.set(p.category_slug, p.category);
  });
  return Array.from(map, ([slug, name]) => ({ slug, name }));
});

const categoryMeta = {
  'weight-loss': {
    title: 'Weight Loss',
    blurb: 'Clinically-proven GLP-1 options to help you lose weight and keep it off.',
    gradient: 'from-amber-50 via-orange-50 to-orange-100',
  },
  'sexual-health': {
    title: 'Sexual Health',
    blurb: 'Proven treatments to boost performance and confidence.',
    gradient: 'from-cyan-50 via-cyan-50 to-blue-100',
  },
  'hair-health': {
    title: 'Hair Health',
    blurb: 'Evidence-based solutions to slow loss and encourage regrowth.',
    gradient: 'from-emerald-50 via-emerald-50 to-teal-100',
  },
};

const pageMeta = computed(() => {
  if (activeCategorySlug.value && categoryMeta[activeCategorySlug.value]) {
    return categoryMeta[activeCategorySlug.value];
  }
  return {
    title: 'All Treatments',
    blurb: 'Explore our most trusted, high‑quality treatments across categories.',
    gradient: 'from-neutral-50 via-neutral-50 to-slate-100',
  };
});

const inStockOnly = ref(false);

const filteredProducts = computed(() => {
  let list = products;
  const slug = activeCategorySlug.value;
  if (slug) list = list.filter(p => p.category_slug === slug);
  if (inStockOnly.value) list = list.filter(p => p.stock);
  return list;
});

const resultsCount = computed(() => filteredProducts.value.length);

function clearFilters() {
  inStockOnly.value = false;
}
</script>

<template>
  <div class="bg-neutral-50 min-h-screen">
    <Header />

    <!-- Page Intro / Breadcrumbs -->
    <section class="pt-8 sm:pt-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="text-sm text-neutral-600 mb-4 flex items-center gap-1">
          <RouterLink :to="{ name: 'Home' }" class="hover:text-neutral-900">Home</RouterLink>
          <IconChevronRight class="w-4 h-4" />
          <RouterLink :to="{ name: 'Treatments' }" class="hover:text-neutral-900">Treatments</RouterLink>
          <template v-if="activeCategorySlug">
            <IconChevronRight class="w-4 h-4" />
            <span class="text-neutral-900 font-medium">
              {{categories.find(c => c.slug === activeCategorySlug)?.name || pageMeta.title}}
            </span>
          </template>
        </nav>

        <!-- Hero header area -->
        <div :class="['rounded-2xl p-6 sm:p-8 border', `bg-gradient-to-br ${pageMeta.gradient}`, 'border-neutral-200']">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 class="text-3xl sm:text-4xl font-bold text-neutral-900 tracking-tight">
                {{ activeCategorySlug ? `${pageMeta.title} Treatments` : pageMeta.title }}
              </h1>
              <p class="text-neutral-700 mt-2 max-w-2xl">{{ pageMeta.blurb }}</p>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm text-neutral-700 bg-white border border-neutral-200 rounded-full px-3 py-1">
                {{ resultsCount }} result{{ resultsCount === 1 ? '' : 's' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Category chips + filters -->
    <section class="mt-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <!-- Category chips -->
          <div class="flex flex-wrap gap-2">
            <RouterLink :to="{ name: 'Treatments' }" class="px-4 py-2 rounded-full text-sm border transition-all"
              :class="!activeCategorySlug ? 'bg-black text-white border-black' : 'bg-white text-neutral-800 border-neutral-300 hover:border-neutral-400'">
              All
            </RouterLink>
            <RouterLink v-for="cat in categories" :key="cat.slug"
              :to="{ name: 'Treatments', params: { category_slug: cat.slug } }"
              class="px-4 py-2 rounded-full text-sm border transition-all"
              :class="activeCategorySlug === cat.slug ? 'bg-black text-white border-black' : 'bg-white text-neutral-800 border-neutral-300 hover:border-neutral-400'">
              {{ cat.name }}
            </RouterLink>
          </div>

          <!-- Simple filters -->
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-2 bg-white border border-neutral-300 rounded-full px-3 py-1.5">
              <IconFilter class="w-4 h-4 text-neutral-600" />
              <label class="flex items-center gap-2 text-sm text-neutral-800 cursor-pointer select-none">
                <input type="checkbox" v-model="inStockOnly" class="accent-black" />
                In stock only
              </label>
              <button v-if="inStockOnly" @click="clearFilters"
                class="ml-2 text-xs text-neutral-600 hover:text-neutral-900 flex items-center gap-1">
                <IconX class="w-4 h-4" /> Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Results Grid / Empty state -->
    <section class="py-8 sm:py-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div v-if="resultsCount === 0" class="bg-white border border-neutral-200 rounded-2xl p-10 text-center">
          <div class="flex flex-col items-center justify-center gap-3">
            <IconMoodSad class="w-10 h-10 text-neutral-500" />
            <h3 class="text-lg font-semibold text-neutral-900">No treatments found</h3>
            <p class="text-neutral-600 max-w-md">Try clearing filters or exploring another category.</p>
            <div class="flex gap-2 mt-2">
              <button @click="clearFilters"
                class="bg-black text-white px-5 py-2 rounded-full text-sm hover:bg-neutral-800 transition">
                Clear filters
              </button>
              <RouterLink :to="{ name: 'Treatments' }"
                class="bg-white text-neutral-900 border border-neutral-300 px-5 py-2 rounded-full text-sm hover:border-neutral-400 transition">
                View all
              </RouterLink>
            </div>
          </div>
        </div>

        <div v-else class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <ProductItem v-for="product in filteredProducts" :key="product.slug" :product="product" />
        </div>
      </div>
    </section>

    <Footer />
  </div>
</template>
