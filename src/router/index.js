import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/pages/LandingPage.vue'),
    meta: {
      title: 'ConnectRx - Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
  {
    path: '/treatments/:category_slug?',
    name: 'Treatments',
    component: () => import('@/pages/Treatments.vue'),
    meta: {
      title: 'ConnectRx - Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
  {
    path: '/treatment/:slug',
    name: 'TreatmentOverview',
    component: () => import('@/pages/TreatmentOverview.vue'),
    meta: {
      title: 'ConnectRx - Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else if (to.hash) {
      return { el: to.hash, behavior: 'smooth' };
    } else if (from.path === to.path) {
      return { top: 0 };
    } else {
      return { top: 0 };
    }
  },
});

router.beforeEach((to, from, next) => {
  document.title = to.meta.title;
  next();
});

export default router;
