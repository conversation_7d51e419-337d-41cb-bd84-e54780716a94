<script setup>
import {
  IconBrandFacebook,
  IconBrandInstagram,
} from "@tabler/icons-vue";
</script>

<template>
  <!-- Footer -->
  <footer
    class="bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 border-t border-neutral-800/60 pt-20 pb-10 w-full">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 relative z-10 mb-12">
        <div>
          <h3 class="text-neutral-200 text-base font-semibold mb-4">Categories</h3>
          <ul class="space-y-3">
            <li>
              <RouterLink :to="{ name: 'Treatments', params: { category_slug: 'weight-loss' } }"
                class="text-neutral-300 hover:text-amber-400 transition-colors">Weight Loss</RouterLink>
            </li>
            <li>
              <RouterLink :to="{ name: 'Treatments', params: { category_slug: 'sexual-health' } }"
                class="text-neutral-300 hover:text-amber-400 transition-colors">Sexual Health</RouterLink>
            </li>
            <li>
              <RouterLink :to="{ name: 'Treatments', params: { category_slug: 'hair-health' } }"
                class="text-neutral-300 hover:text-amber-400 transition-colors">Hair</RouterLink>
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-neutral-200 text-base font-semibold mb-4">Follow us</h3>
          <ul class="space-y-3">
            <li class="flex items-center gap-2">
              <IconBrandInstagram class="w-5 h-5 text-neutral-300" /> <a href="#"
                class="text-neutral-300 hover:text-amber-400 transition-colors">Instagram</a>
            </li>
            <li class="flex items-center gap-2">
              <IconBrandFacebook class="w-5 h-5 text-neutral-300" />
              <a href="#" class="text-neutral-300 hover:text-amber-400 transition-colors">Facebook</a>
            </li>
          </ul>
        </div>
        <div class="md:col-span-2">
          <h3 class="text-neutral-200 text-base font-semibold mb-4">Contact</h3>
          <div class="mb-2 text-neutral-400 text-sm">For support or questions about your order:</div>
          <a href="mailto:<EMAIL>"
            class="text-neutral-100 font-medium hover:text-amber-400 transition-colors inline-block mb-4"><EMAIL></a>
          <div class="mb-2 text-neutral-400 text-sm">For all other questions:</div>
          <a href="mailto:<EMAIL>"
            class="text-neutral-100 font-medium hover:text-amber-400 transition-colors inline-block"><EMAIL></a>
        </div>
      </div>
      <!-- Big Brand Name -->
      <div class="w-full flex flex-col items-center mb-12">
        <span
          class="text-[12vw] font-extrabold text-neutral-700/40 tracking-tighter text-center leading-none select-none">ConnectRx</span>
      </div>
      <div class="border-t border-neutral-700 pt-8 mt-8">
        <div class="flex flex-wrap justify-center items-center gap-8 mb-6">
          <img src="@/assets/images/hipaa-compliant.png" alt="HIPAA Compliant" class="h-12 w-auto object-contain" />
          <img src="@/assets/images/legit-script.png" alt="LegitScript Certified" class="h-12 w-auto object-contain" />
          <img src="@/assets/images/secure.png" alt="Secure & Encrypted" class="h-12 w-auto object-contain" />
        </div>
        <div class="flex flex-col md:flex-row justify-center items-center gap-4 text-neutral-500 text-xs">
          <a href="#" class="hover:text-amber-400 transition-colors">Terms &amp; Conditions</a>
          <span class="hidden md:inline">|</span>
          <a href="#" class="hover:text-amber-400 transition-colors">Privacy Policy</a>
          <span class="hidden md:inline">|</span>
          <span>&copy; 2025 ConnectRx. All rights reserved.</span>
        </div>
      </div>
    </div>
  </footer>
</template>
