<script setup>
const props = defineProps({
  product: Object,
});
</script>

<template>
  <RouterLink v-if="product" :to="{ name: 'TreatmentOverview', params: { slug: product.slug } }"
    class="relative bg-neutral-50 rounded-2xl p-6 flex flex-col min-h-[420px] overflow-hidden hover:shadow-lg transition-all duration-300 border border-neutral-200">
    <div class="flex justify-between mb-4">
      <div class="text-xs font-medium px-2 py-1 rounded bg-amber-50 text-amber-700">
        {{ product.category }}
      </div>
      <div>
        <span v-if="product.stock"
          class="inline-block text-green-700 bg-green-50 border border-green-200 text-xs px-3 py-0.5 rounded">
          In Stock
        </span>
        <span v-else class="inline-block text-red-700 bg-red-50 border border-red-200 text-xs px-3 py-0.5 rounded">
          Out of Stock</span>
      </div>
    </div>
    <div>
      <h3 class="text-xl font-medium text-neutral-900 mb-2 z-10 relative">{{ product.name }}</h3>
      <p class="text-sm text-neutral-600 mb-2 leading-relaxed z-10 relative">{{ product.product_summary }}</p>
    </div>
    <div class="flex justify-center items-center h-full">
      <img :src="product.product_image" :alt="product.name"
        class="w-5/7 min-w-[120px] max-w-[330px] object-contain z-0 select-none pointer-events-none" />
    </div>
    <div class="mt-auto gap-3 z-10 relative flex flex-col justify-end">
      <a :href="product.start_visit_url" @click.stop="() => { }"
        class="bg-white text-black border border-gray-400 px-5 py-2 rounded-full hover:bg-gray-800 hover:text-white transition-all duration-200 font-medium cursor-pointer text-center"
        :disabled="!product.stock" :class="{ 'opacity-60 cursor-not-allowed': !product.stock }">
        Get Started
      </a>
    </div>
  </RouterLink>
</template>