<script setup>
import products from "@/data/products.json";
import { computed } from 'vue';
import ProductItem from "./ProductItem.vue";

const topProducts = computed(() => {
  return products.sort(() => 0.5 - Math.random()).slice(0, 6);
});
</script>

<template>
  <!-- Product Highlights -->
  <section class="py-24 bg-neutral-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="w-full flex justify-center items-center mb-10 sm:mb-20">
        <h2 class="text-4xl lg:text-5xl font-bold text-neutral-900 flex-1 text-center">
          Top products
        </h2>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <ProductItem v-for="product in topProducts" :key="product.slug" :product="product" />
      </div>
      <div class="mt-20 text-center">
        <RouterLink :to="{ name: 'Treatments' }"
          class="bg-neutral-900 text-white px-6 py-3 rounded-full hover:bg-neutral-800 transition-all duration-200 font-medium cursor-pointer">
          Explore All Treatments
        </RouterLink>
      </div>
    </div>
  </section>
</template>
