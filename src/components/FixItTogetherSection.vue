<script setup>
import { IconArrowRight } from "@tabler/icons-vue";
</script>

<template>
  <!-- Let's Fix It Together Section -->
  <section class="py-12 pb-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-10 sm:mb-20">
        <h2 class="text-4xl lg:text-6xl font-bold text-neutral-900">
          Let's fix it together.
        </h2>
      </div>

      <div class="grid md:grid-cols-3 gap-6">
        <!-- Sexual Health Card -->
        <div class="relative rounded-3xl overflow-hidden min-h-[500px] group cursor-pointer group">
          <div class="absolute inset-0 bg-black/20"></div>
          <img src="@/assets/images/cat-sexual-health.jpg" alt="Sexual Health"
            class="absolute inset-0 w-full h-full object-cover group-hover:scale-110 transition-transform duration-300" />
          <div class="relative z-10 p-8 h-full flex flex-col justify-between">
            <div class="space-y-4">
              <div class="bg-white/90 backdrop-blur-sm text-black text-sm font-medium px-3 py-1 rounded-full w-fit">
                Sexual health
              </div>
              <h3 class="text-white text-5xl font-bold mb-6 leading-tight">
                Have<br>
                better<br>
                sex.
              </h3>
            </div>
            <div>
              <RouterLink :to="{ name: 'Treatments', params: { category_slug: 'sexual-health' } }"
                class="bg-yellow-300 hover:bg-yellow-200 text-black font-semibold px-6 py-3 rounded-full transition-all duration-300 inline-flex items-center">
                Get care
                <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- Hair Health Card -->
        <div class="relative rounded-3xl overflow-hidden min-h-[500px] group cursor-pointer group">
          <img src="@/assets/images/cat-hair-health.jpg" alt="Hair Health"
            class="absolute inset-0 w-full h-full object-cover group-hover:scale-110 transition-transform duration-300" />
          <div class="relative z-10 p-8 h-full flex flex-col justify-between">
            <div class="space-y-4">
              <div class="bg-white/90 backdrop-blur-sm text-black text-sm font-medium px-3 py-1 rounded-full w-fit">
                Hair health
              </div>
              <h3 class="text-black text-5xl font-bold mb-6 leading-tight">
                Regrow<br>
                your<br>
                hair.
              </h3>
            </div>
            <div>
              <RouterLink :to="{ name: 'Treatments', params: { category_slug: 'hair-health' } }"
                class="bg-yellow-300 hover:bg-yellow-200 text-black font-semibold px-6 py-3 rounded-full transition-all duration-300 inline-flex items-center">
                Get care
                <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- Weight Loss Card -->
        <div class="relative rounded-3xl overflow-hidden to-black min-h-[500px] group cursor-pointer group">
          <div class="absolute inset-0 bg-black/30"></div>
          <img src="@/assets/images/cat-weight-loss.jpg" alt="Weight Loss"
            class="absolute inset-0 w-full h-full object-cover group-hover:scale-110 transition-transform duration-300" />
          <div class="relative z-10 p-8 h-full flex flex-col justify-between">
            <div class="space-y-4">
              <div class="bg-white/90 backdrop-blur-sm text-black text-sm font-medium px-3 py-1 rounded-full w-fit">
                Weight loss
              </div>
              <h3 class="text-white text-5xl font-bold mb-6 leading-tight">
                Lose<br>
                weight.
              </h3>
            </div>
            <div>
              <RouterLink :to="{ name: 'Treatments', params: { category_slug: 'weight-loss' } }"
                class="bg-yellow-300 hover:bg-yellow-200 text-black font-semibold px-6 py-3 rounded-full transition-all duration-300 inline-flex items-center">
                Get care
                <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
