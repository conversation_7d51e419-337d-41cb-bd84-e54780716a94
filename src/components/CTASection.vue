<template>
  <!-- CTA Section -->
  <section class="relative py-28 bg-gradient-to-br from-amber-50 via-white to-amber-100 overflow-hidden">
    <!-- Animated background accent -->
    <div
      class="absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[700px] bg-amber-200/20 rounded-full blur-3xl animate-pulse-slow z-0">
    </div>
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
      <h2 class="text-4xl lg:text-5xl font-extrabold text-neutral-900 mb-4 animate-fade-in">
        Take the First Step Toward a Healthier You
      </h2>
      <p class="text-2xl text-amber-600 font-semibold mb-4 animate-fade-in delay-100">
        Your journey to better health starts here.
      </p>
      <p class="text-xl text-neutral-700 mb-12 max-w-2xl mx-auto leading-relaxed animate-fade-in delay-200">
        Join thousands of satisfied customers who trust ConnectRx for their healthcare needs. Experience the
        difference with premium quality, expert support, and exceptional service.
      </p>
      <div class="flex flex-col sm:flex-row gap-6 justify-center animate-fade-in delay-300">
        <RouterLink :to="{ name: 'Treatments' }"
          class="bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-amber-500 hover:text-white transition-all duration-200 shadow-lg cursor-pointer">
          Explore Treatments
        </RouterLink>
      </div>
    </div>
  </section>
</template>

<style scoped>
@keyframes pulse-slow {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
