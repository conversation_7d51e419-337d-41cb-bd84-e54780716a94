<script setup>
import { ref } from "vue";

const faqs = ref([
  {
    question: "How does ConnectRx work?",
    answer:
      "ConnectRx is a telehealth platform that connects you with US-licensed physicians for a variety of treatments. Simply complete an online visit, and a physician will review your information and, if appropriate, write a prescription. Your medication will then be shipped to your door in discreet packaging.",
    open: false,
  },
  {
    question: "Is ConnectRx legit?",
    answer:
      "Yes, ConnectRx is a legitimate telehealth platform. We only work with US-licensed physicians and FDA-regulated pharmacies to ensure you receive the highest quality care and medication.",
    open: false,
  },
  {
    question: "What treatments does ConnectRx offer?",
    answer:
      "ConnectRx offers a variety of treatments for weight loss, sexual health, daily wellness, and hair loss. You can explore all of our treatments on our website.",
    open: false,
  },
  {
    question: "How much does ConnectRx cost?",
    answer:
      "The cost of ConnectRx varies depending on the treatment you choose. We offer transparent pricing with no hidden fees. You can find the price of each treatment on our website.",
    open: false,
  },
  {
    question: "Do I need a prescription?",
    answer:
      "Yes, all of our treatments require a prescription from a US-licensed physician. You can get a prescription by completing an online visit with one of our physicians.",
    open: false,
  },
]);

const toggle = (index) => {
  faqs.value = faqs.value.map((faq, i) => {
    if (i === index) {
      faq.open = !faq.open;
    } else {
      faq.open = false;
    }
    return faq;
  });
};
</script>

<template>
  <!-- FAQ Section -->
  <section class="py-20 bg-gradient-to-b from-white via-gray-50/30 to-gray-50">
    <div class="max-w-7xl mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <div class="lg:sticky lg:top-32">
          <h2 class="text-4xl lg:text-5xl font-medium text-gray-900 mb-6">
            Frequently asked <span class="text-amber-500 italic">questions</span>
          </h2>
          <p class="text-lg text-gray-600 mb-8">
            Find answers to common questions about our medication subscriptions and medical review process.
          </p>
        </div>
        <div class="space-y-4">
          <div v-for="(faq, index) in faqs" :key="index" class="border border-gray-200 rounded-lg">
            <button @click="toggle(index)"
              class="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 transition-colors">
              <span class="font-semibold text-gray-900">
                {{ faq.question }}
              </span>
              <svg class="w-6 h-6 text-gray-500 transition-transform duration-200" :class="{ 'rotate-45': faq.open }"
                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                </path>
              </svg>
            </button>
            <div class="overflow-hidden transition-all duration-300 ease-in-out"
              :class="{ 'max-h-96': faq.open, 'max-h-0': !faq.open }">
              <div class="px-6 pb-6 text-gray-600">
                <p>{{ faq.answer }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
