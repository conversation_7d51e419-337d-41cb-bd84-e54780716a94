/**
 * Checks if a string is a valid email address.
 *
 * @param {string} str - The string to check.
 * @returns {boolean} - True if the string is a valid email address, false otherwise.
 */
export function isEmail(str: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(str)
}

/**
 * Checks if a value is empty.
 *
 * @param {any} value - The value to check.
 * @returns {boolean} - True if the value is empty, false otherwise.
 */
export function isEmpty(value: any): boolean {
  return (
    value == null ||
    (typeof value === 'string' && value.trim() === '') ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === 'object' && Object.keys(value).length === 0)
  )
}

/**
 * Checks if an object is empty.
 *
 * @param {any} obj - The object to check.
 * @returns {boolean} - True if the object is empty, false otherwise.
 */
export function isEmptyObject(obj: any): boolean {
  return Object.keys(obj).length === 0
}
