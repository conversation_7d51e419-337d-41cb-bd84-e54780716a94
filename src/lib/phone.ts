/**
 * Cleans a phone number to digits only.
 * @example cleanPhone('+1 (*************') -> '15551234567'
 */
export function cleanPhone(phone: string): string {
  return phone.replace(/\D/g, '')
}

/**
 * Formats a 10 or 11-digit US phone number to (XXX) XXX-XXXX or +1 (XXX) XXX-XXXX.
 * Gracefully handles missing digits.
 * @example formatPhone('15551234567') -> '+1 (*************'
 * @example formatPhone('5551234567') -> '(*************'
 */
export function formatPhone(phone: string): string {
  const digits = cleanPhone(phone)

  if (digits.length === 11 && digits.startsWith('1')) {
    return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`
  }

  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`
  }

  // fallback
  return phone
}
